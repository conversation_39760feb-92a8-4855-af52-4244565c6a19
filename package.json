{"name": "connect", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "gsap": "^3.13.0", "lenis": "^1.3.9", "lucide-react": "^0.542.0", "motion": "^12.23.12", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "rough-notation": "^0.5.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}