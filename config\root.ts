import type { TFooterLink } from "@/types";

export type TProject = {
  title: string;
  description: string;
  src: string;
  link: string;
  color: string;
};
export const projects: TProject[] = [
  {
    title: "<PERSON>",
    description:
      "Originally hailing from Austria, Berlin-based photographer <PERSON> is a young creative brimming with talent and ideas.",
    src: "/images/1.jpg",
    link: "https://www.ignant.com/2023/03/25/ad2186-matthias-leidingers-photographic-exploration-of-awe-and-wonder/",
    color: "#BBACAF",
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    description:
      "This is a story on the border between reality and imaginary, about the contradictory feelings that the insularity of a rocky, arid, and wild territory  .",
    src: "/images/2.jpg",
    link: "https://www.ignant.com/2022/09/30/clement-chapillon-questions-geographical-and-mental-isolation-with-les-rochers-fauves/",
    color: "#977F6D",
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    description:
      "Though he views photography as a medium for storytelling, <PERSON><PERSON><PERSON>’s images don’t insist on a narrative. Both crisp and ethereal, they’re encoded with an ",
    src: "/images/5.jpg",
    link: "https://www.ignant.com/2023/10/28/capturing-balis-many-faces-zissou-documents-the-sacred-and-the-mundane-of-a-fragile-island/",
    color: "#C2491D",
  },
  {
    title: "<PERSON> Svold and Ulrik Hasemann",
    description:
      "The coastlines of Denmark are documented in tonal colors in a pensive new series by Danish photographers Ulrik Hasemann and Mathias Svold; an ongoing project ",
    src: "/images/6.jpg",
    link: "https://www.ignant.com/2019/03/13/a-photographic-series-depicting-the-uncertain-future-of-denmarks-treasured-coastlines/",
    color: "#B62429",
  },
  {
    title: "Mark Rammers",
    description:
      "Dutch photographer Mark Rammers has shared with IGNANT the first chapter of his latest photographic project, ‘all over again’—captured while in residency at ",
    src: "/images/a.jpg",
    link: "https://www.ignant.com/2023/04/12/mark-rammers-all-over-again-is-a-study-of-regret-and-the-willingness-to-move-forward/",
    color: "#88A28D",
  },
];

export const footerLinks: TFooterLink[] = [
  {
    title: "Newsroom",
    links: [
      { name: "Latest News", href: "/", external: false },
      { name: "Top Stories", href: "/", external: false },
      { name: "Editor's Picks", href: "/", external: false },
    ],
  },
  {
    title: "Company",
    links: [
      { name: "About Us", href: "/", external: false },
      { name: "Careers", href: "/", external: false },
      { name: "Press", href: "/", external: false },
      { name: "Contact", href: "/", external: false },
    ],
  },
  {
    title: "For Business",
    links: [
      { name: "Advertise with Us", href: "/", external: false },
      { name: "Media Kit", href: "/", external: false },
      { name: "Partner with Us", href: "/", external: false },
    ],
  },
  {
    title: "More",
    links: [
      { name: "Newsletter", href: "/", external: false },
      { name: "Mobile App", href: "/", external: false },
      { name: "RSS Feeds", href: "/", external: false },
      { name: "Help Center", href: "/", external: false },
    ],
  },
  {
    title: "Terms & Policies",
    links: [
      { name: "Terms of Use", href: "/", external: false },
      { name: "Privacy Policy", href: "/", external: false },
      { name: "Cookie Policy", href: "/", external: false },
      { name: "Editorial Policy", href: "/", external: false },
    ],
  },
  {
    title: "Safety",
    links: [
      { name: "Fact-Checking", href: "/", external: false },
      { name: "Corrections", href: "/", external: false },
      { name: "Trust & Transparency", href: "/", external: false },
    ],
  },
  {
    title: "Follow Us",
    links: [
      { name: "Facebook", href: "/", external: true },
      { name: "Twitter", href: "/", external: true },
      { name: "Instagram", href: "/", external: true },
      { name: "YouTube", href: "/", external: true },
    ],
  },
  {
    title: "Sections",
    links: [
      { name: "Politics", href: "/", external: false },
      { name: "Business", href: "/", external: false },
      { name: "Technology", href: "/", external: false },
      { name: "Health", href: "/", external: false },
    ],
  },
  {
    title: "Resources",
    links: [
      { name: "Media Resources", href: "/", external: false },
      { name: "Author Guidelines", href: "/", external: false },
      { name: "News Archive", href: "/", external: false },
    ],
  },
  {
    title: "Community",
    links: [
      { name: "Events", href: "/", external: false },
      { name: "Reader Stories", href: "/", external: false },
      { name: "Submit News", href: "/", external: false },
    ],
  },
];

export const citiesInRwanda = [
  { name: "Kigali", province: "Kigali City" },
  { name: "Huye", province: "Southern Province" },
  { name: "Muhanga", province: "Southern Province" },
  { name: "Nyanza", province: "Southern Province" },
  { name: "Ruhango", province: "Southern Province" },
  { name: "Rubavu", province: "Western Province" },
  { name: "Rusizi", province: "Western Province" },
  { name: "Karongi", province: "Western Province" },
  { name: "Nyamasheke", province: "Western Province" },
  { name: "Musanze", province: "Northern Province" },
  { name: "Gicumbi", province: "Northern Province" },
  { name: "Burera", province: "Northern Province" },
  { name: "Rwamagana", province: "Eastern Province" },
  { name: "Kayonza", province: "Eastern Province" },
  { name: "Ngoma", province: "Eastern Province" },
  { name: "Bugesera", province: "Eastern Province" },
];
